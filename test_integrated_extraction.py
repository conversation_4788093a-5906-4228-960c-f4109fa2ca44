#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试整合后的区域和 tab 内容提取
"""

import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from src.extractors.enhanced_cms_extractor import EnhancedCMSExtractor


def test_integrated_extraction():
    """测试整合后的提取功能"""
    
    print("🧪 测试整合后的 Data Lake Storage 提取功能")
    
    # 初始化提取器
    extractor = EnhancedCMSExtractor(
        output_dir="output",
        config_file="data/configs/soft-category.json"
    )
    
    # 测试文件路径
    html_file = "data/prod-html/storage_data-lake_index.html"
    
    if not Path(html_file).exists():
        print(f"❌ 测试文件不存在: {html_file}")
        return
    
    print(f"📁 测试文件: {html_file}")
    
    try:
        # 提取内容
        result = extractor.extract_cms_content(html_file)
        
        print("\n📊 提取结果概览:")
        print(f"  标题: {result.get('Title', 'N/A')}")
        print(f"  产品: {result.get('MSServiceName', 'N/A')}")
        print(f"  有区域: {result.get('HasRegion', False)}")
        
        # 检查区域内容
        if result.get('HasRegion'):
            print("\n🌏 区域内容分析:")
            region_keys = [key for key in result.keys() if key.endswith('Content') and key != 'NoRegionContent']
            
            for key in region_keys:
                value = result[key]
                if value:  # 只显示有内容的区域
                    if isinstance(value, list):
                        print(f"  {key}: 数组格式，包含 {len(value)} 个 tab")
                        for i, tab in enumerate(value):
                            if isinstance(tab, dict) and 'tabName' in tab:
                                content_length = len(tab.get('content', ''))
                                print(f"    Tab {i+1}: {tab['tabName']} ({content_length} 字符)")
                                
                                # 显示内容预览
                                content_preview = tab.get('content', '')[:200]
                                if content_preview:
                                    print(f"      预览: {content_preview}...")
                            else:
                                print(f"      Tab {i+1}: 格式异常")
                    else:
                        print(f"  {key}: 字符串格式 ({len(str(value))} 字符)")
                        # 显示内容预览
                        content_preview = str(value)[:200]
                        if content_preview:
                            print(f"    预览: {content_preview}...")
        else:
            print("\n📄 无区域内容分析:")
            no_region_content = result.get('NoRegionContent', '')
            if isinstance(no_region_content, list):
                print(f"  NoRegionContent: 数组格式，包含 {len(no_region_content)} 个 tab")
                for i, tab in enumerate(no_region_content):
                    if isinstance(tab, dict) and 'tabName' in tab:
                        content_length = len(tab.get('content', ''))
                        print(f"    Tab {i+1}: {tab['tabName']} ({content_length} 字符)")
            else:
                print(f"  NoRegionContent: 字符串格式 ({len(str(no_region_content))} 字符)")
        
        # 检查提取元数据
        metadata = result.get('extraction_metadata', {})
        print(f"\n🔍 提取元数据:")
        print(f"  提取器版本: {metadata.get('extractor_version', 'N/A')}")
        print(f"  检测到的区域数: {metadata.get('regions_detected', 0)}")
        print(f"  定价表格数: {metadata.get('pricing_tables_found', 0)}")
        print(f"  使用产品配置: {metadata.get('product_config_used', False)}")
        
        # 保存结果
        output_file = "output/integrated_extraction_test_result.json"
        Path("output").mkdir(exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 结果已保存到: {output_file}")
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_integrated_extraction()
