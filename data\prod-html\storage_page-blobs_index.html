<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="企业云存储, 数据存储, 云存储,Azure" name="keywords"/>
  <meta content="查看 Azure 页 Blob 存储（用于数据存储的企业级云存储服务）的定价详情。无前期成本。即用即付。免费试用。" name="description"/>
  <title>
   价格详情-存储-页Blob-Azure云计算
  </title>
  <link href="../../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/storage/page-blobs/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
    window.currentLocale = "zh-CN";
    window.headerTimestamp = "2019/1/23 8:25:24";
    window.footerTimestamp = "2019/1/8 8:07:06";
    window.locFileTimestamp = "2018/11/29 7:49:17";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
  </script>
  <style>
   @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <style type="text/css">
        .pricing-detail-tab .tab-nav {
                            padding-left: 0 !important;
                            margin-top: 5px;
                            margin-bottom: 0;
                            overflow: hidden;
                        }

                        .pricing-detail-tab .tab-nav li {
                            list-style: none;
                            float: left;
                        }

                        .pricing-detail-tab .tab-nav li.active a {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-nav li.active a:hover {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel {
                            display: none;
                        }

                        .pricing-detail-tab .tab-content .tab-panel.show-md {
                            display: block;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                            padding-left: 5px;
                            padding-right: 5px;
                            color: #00a3d9;
                            background-color: #FFF;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pure-content .technical-azure-selector p a, .pure-content .technical-azure-selector table a {
                            background: 0 0;
                            padding: 0;
                            margin: 0 6px;
                            height: 21px;
                            line-height: 22px;
                            font-size: 14px;
                            color: #00a3d9;
                            float: none;
                            display: inline;
                        }
       </style>
       <tags ms.date="09/30/2015" ms.service="storage-unmanaged-disks-page-blobs" wacn.date="11/27/2015">
       </tags>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/storage.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/<EMAIL>"/>
          <h2>
           页 Blob
           <span>
            Page Blob
           </span>
          </h2>
          <h4>
           针对随机读写选项进行了优化
          </h4>
         </div>
        </div>
       </div>
       <div class="pricing-page-section">
        <p>
         页 Blob 针对随机读写选项进行了优化。页 Blob 非常适合用于需要能够覆盖已知地址上随机一小段的方案，例如存储基于索引的数据结构和稀疏数据结构。可通过 REST 协议访问页 Blob 或将其附加到虚拟机，以非托管磁盘的方式支持磁盘通信。
        </p>
        <div>
         <h2>
          定价详细信息
         </h2>
         <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
          <div class="tab-container-container">
           <div class="tab-container-box">
            <div class="tab-container">
             <div class="dropdown-container software-kind-container" style="display: none">
              <label>
               OS/软件：
              </label>
              <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
               <span class="selected-item">
                Page Blobs
               </span>
               <i class="icon">
               </i>
               <ol class="tab-items">
                <li class="active">
                 <a data-href="#tabContent1" href="javascript:void(0)" id="home_page-blobs">
                  Page Blobs
                 </a>
                </li>
               </ol>
              </div>
              <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
               <option data-href="#tabContent1" selected="selected" value="Page Blobs">
                Page Blobs
               </option>
              </select>
             </div>
             <div class="dropdown-container region-container">
              <label>
               地区：
              </label>
              <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
               <span class="selected-item">
                中国东部3
               </span>
               <i class="icon">
               </i>
               <ol class="tab-items">
                <li class="active"><a href="javascript:void(0)" data-href="#east-china3" id="east-china3">中国东部 3</a></li>
                <li>
                 <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                  中国北部 3
                 </a>
                </li>
                <li>
                 <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                  中国东部 2
                 </a>
                </li>
                <li>
                 <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                  中国北部 2
                 </a>
                </li>
                <li>
                 <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                  中国东部
                 </a>
                </li>
                <li>
                 <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                  中国北部
                 </a>
                </li>
               </ol>
              </div>
              <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
               <option selected="selected" data-href="#east-china3"
                                                            value="east-china3">中国东部 3
                                                    </option>
               <option data-href="#north-china3" value="north-china3">
                中国北部 3
               </option>
               <option data-href="#east-china2" value="east-china2">
                中国东部 2
               </option>
               <option data-href="#north-china2" value="north-china2">
                中国北部 2
               </option>
               <option data-href="#east-china" value="east-china">
                中国东部
               </option>
               <option data-href="#north-china" value="north-china">
                中国北部
               </option>
              </select>
             </div>
             <div class="clearfix">
             </div>
            </div>
           </div>
          </div>
          <div class="tab-content">
           <div class="tab-panel" id="tabContent1">
            <div class="category-container-container">
             <div class="category-container-box">
              <div class="category-container">
               <span class="category-title hidden-lg hidden-md">
                类别：
               </span>
               <ul class="os-tab-nav category-tabs hidden-xs hidden-sm">
                <li class="active">
                 <a data-href="#tabContent-gv2" href="javascript:void(0)">
                  常规用途 v2
                 </a>
                </li>
                <li>
                 <a data-href="#tabContent-gv1" href="javascript:void(0)">
                  常规用途 v1
                 </a>
                </li>
               </ul>
               <select class="dropdown-select category-tabs hidden-lg hidden-md">
                <option data-href="#tabContent-gv2" value="gv2">
                 常规用途 v2
                </option>
                <option data-href="#tabContent-gv1" value="gv1">
                 常规用途 v1
                </option>
               </select>
              </div>
             </div>
            </div>
            <div class="tab-content">
             <div class="tab-panel" id="tabContent-gv2">
              <p>
               常规用途 v2 的定价针对 GB 存储最低价格进行了优化，通过它可以访问最新的 Azure 存储功能，包括冷存储和存档存储。使用这些帐户可访问块 Blob、页 Blob、文件和队列。
              </p>
              <div class="scroll-table" style="display: block;">
               <h4>
                高级页 Blob 的价格
               </h4>
               <p>
                高级非托管磁盘是基于固态硬盘 (SSD) 的高性能存储器，旨在通过极大吞吐量和极低延迟来支持 I/O 密集型工作负荷。借助高级非托管磁盘，可以设置永久磁盘，并配置其大小和性能特征。
               </p>
               <p>
                高级非托管磁盘的总费用取决于磁盘的大小和数量，并且受到
                <a href="../../data-transfer/index.html" id="pricing_storage_data-transfer-1">
                 数据传输
                </a>
                量的影响。这些磁盘大小提供不同的每秒输入/输出操作数
                                                        (IOP)、吞吐量限制和每 GB 每月价格。可选择最符合应用程序的所需存储大小、IOP 和吞吐量的选项。可以将若干个永久磁盘附加到 VM 并对于每个 VM 配置最多 64 TB 的存储，，每个 VM 的磁盘吞吐量可达到
                                                        80,000（每秒输入/输出操作数）和 1600 MB/秒，读取操作的延迟少于 1 毫秒。高级非托管磁盘受专门针对高级磁盘的 DS 系列、DSv2 系列、 FS 系列 VM 大小的支持。DS 系列虚拟机的定价和计费标准与
                                                        D 系列虚拟机相同。
               </p>
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
                <div class="ms-date">
                 *每月价格估算基于每个月 744 小时的使用量。
                </div>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv2-premium" width="100%">
                <tbody>
                 <tr>
                  <th align="left">
                   <strong>
                    页 BLOB 类型
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    P10
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    P20
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    P30
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    P40
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    P50
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    P60
                    <sup>
                     1
                    </sup>
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   磁盘大小
                  </td>
                  <td>
                   128 GB
                  </td>
                  <td>
                   512 GB
                  </td>
                  <td>
                   1 TB
                  </td>
                  <td>
                   2 TB
                  </td>
                  <td>
                   4 TB
                  </td>
                  <td>
                   8 TB
                  </td>
                 </tr>
                 <tr>
                  <td>
                   每月价格
                  </td>
                  <td>
                   ￥132.15
                  </td>
                  <td>
                   ￥490.93
                  </td>
                  <td>
                   ￥906.29
                  </td>
                  <td>
                   ￥1,736.83
                  </td>
                  <td>
                   ￥3,322.63
                  </td>
                  <td>
                   ￥6,343.21
                  </td>
                 </tr>
                 <tr>
                  <td>
                   每个页 Blob 的 IOP
                  </td>
                  <td>
                   500
                  </td>
                  <td>
                   2,300
                  </td>
                  <td>
                   5,000
                  </td>
                  <td>
                   7,500
                  </td>
                  <td>
                   7,500
                  </td>
                  <td>
                   7,500
                  </td>
                 </tr>
                 <tr>
                  <td>
                   每个页 Blob 的吞吐量
                  </td>
                  <td>
                   100 MB / 秒
                  </td>
                  <td>
                   150 MB / 秒
                  </td>
                  <td>
                   200 MB / 秒
                  </td>
                  <td>
                   250 MB / 秒
                  </td>
                  <td>
                   250 MB / 秒
                  </td>
                  <td>
                   250 MB / 秒
                  </td>
                 </tr>
                </tbody>
               </table>
               <div class="tags-date">
                <div class="ms-date">
                 高级磁盘没有事务处理费用。根据支持目标大小的最低类型对高级页 Blob 收费。
                </div>
                <div class="ms-date">
                 <sup>
                  1
                 </sup>
                 Azure 不支持将 P60 作为虚拟机的磁盘。 页 Blob 支持通过 REST 访问 P60。
                </div>
               </div>
              </div>
              <div class="scroll-table" style="display: block;">
                <p>
                 我们还额外支持三个页 blob 类型，这要求你使用 Blob 层集进行显式部署。如果你部署了仅指定目标大小的页 blob 或非托管磁盘，则将向上舍入到上表中列出的受支持的页 blob 类型。
                </p>
                <div class="tags-date">
                 <div class="ms-date">
                  *以下价格均为含税价格。
                 </div>
                 <br/>
                 <div class="ms-date">
                  *每月价格估算基于每个月 744 小时的使用量。
                 </div>
                </div>
                <table cellpadding="0" cellspacing="0" id="page-blobs-gv2-premium-addition-n3" width="100%">
                 <tbody>
                  <tr>
                   <th align="left">
                   </th>
                   <th align="left">
                    <strong>
                     P4
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     P6
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     P15
                    </strong>
                   </th>
                  </tr>
                  <tr>
                   <td>
                    磁盘大小
                   </td>
                   <td>
                    32 GB
                   </td>
                   <td>
                    64 GB
                   </td>
                   <td>
                    256 GB
                   </td>
                  </tr>
                  <tr>
                   <td>
                    每月价格
                   </td>
                   <td>
                    ¥ 63.4728
                   </td>
                   <td>
                    ¥ 122.6844
                   </td>
                   <td>
                    ¥ 340.26
                   </td>
                  </tr>
                  <tr>
                   <td>
                    每个页 Blob 的 IOP
                   </td>
                   <td>
                    120
                   </td>
                   <td>
                    240
                   </td>
                   <td>
                    1,100
                   </td>
                  </tr>
                  <tr>
                   <td>
                    每个页 Blob 的吞吐量
                   </td>
                   <td>
                    25 MB/秒
                   </td>
                   <td>
                    50 MB/秒
                   </td>
                   <td>
                    125 MB/秒
                   </td>
                  </tr>
                 </tbody>
                </table>
                <p>
                  快照价格按每月 LRS￥1.42/GB 或ZRS￥2.14/GB 的费率计费费。
                </p>
               </div>
              <div class="scroll-table" style="display: block;">
               <p>
                我们还额外支持三个页 blob 类型，这要求你使用 Blob 层集进行显式部署。如果你部署了仅指定目标大小的页 blob 或非托管磁盘，则将向上舍入到上表中列出的受支持的页 blob 类型。
               </p>
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
                <div class="ms-date">
                 *每月价格估算基于每个月 744 小时的使用量。
                </div>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv2-premium-addition" width="100%">
                <tbody>
                 <tr>
                  <th align="left">
                  </th>
                  <th align="left">
                   <strong>
                    P4
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    P6
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    P15
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   磁盘大小
                  </td>
                  <td>
                   32 GB
                  </td>
                  <td>
                   64 GB
                  </td>
                  <td>
                   256 GB
                  </td>
                 </tr>
                 <tr>
                  <td>
                   每月价格
                  </td>
                  <td>
                   ¥ 47.53
                  </td>
                  <td>
                   ¥ 91.9
                  </td>
                  <td>
                   ¥ 254.86
                  </td>
                 </tr>
                 <tr>
                  <td>
                   每个页 Blob 的 IOP
                  </td>
                  <td>
                   120
                  </td>
                  <td>
                   240
                  </td>
                  <td>
                   1,100
                  </td>
                 </tr>
                 <tr>
                  <td>
                   每个页 Blob 的吞吐量
                  </td>
                  <td>
                   25 MB/秒
                  </td>
                  <td>
                   50 MB/秒
                  </td>
                  <td>
                   125 MB/秒
                  </td>
                 </tr>
                </tbody>
               </table>
               <p>
                快照价格按每月 LRS￥2.35/GB 的费率计费。
               </p>
              </div>
             
              
               <h4>
                标准页 Blob 的数据存储价格
               </h4>
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
               </div>
               <div class="scroll-table" style="display: block;">
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv2-standard-data-storage" width="100%">
                <tbody>
                 <tr>
                  <th align="left">
                   <strong>
                    本地冗余存储 (LRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    异地冗余存储 (GRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    读取访问跨异地冗余存储 (RA-GRS)
                   </strong>
                  </th>
                </tr>
                 <tr>
                  <td>
                   ¥ 0.4579/GB
                  </td>
                  <td>
                   ¥ 0.6106/GB
                  </td>
                  <td>
                   ¥ 0.7632/GB
                  </td>
                 </tr>
                </tbody>
               </table>
              </div>
              <div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv2-standard-data-storage-N3" width="100%">
                <tbody>
                 <tr>
                  <th align="left">
                   <strong>
                    本地冗余存储 (LRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    异地冗余存储 (GRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    读取访问跨异地冗余存储 (RA-GRS)
                   </strong>
                  </th>
                 <th align="left">
                  <strong>
                    区域冗余存储 (ZRS)
                  </strong>
                 </th>
                </tr>
                 <tr>
                  <td>
                   ¥ 0.4579/GB
                  </td>
                  <td>
                   ¥ 0.6106/GB
                  </td>
                  <td>
                   ¥ 0.7632/GB
                  </td>
                  <td>
                    ¥ 0.5724/GB
                   </td>
                 </tr>
                </tbody>
               </table>
              </div>
               <p>
                <strong>
                 <sup>
                  1
                 </sup>
                </strong>
                1 TB = 1,024 GB
               </p>
              <div class="scroll-table" style="display: block;">
               <h4>
                用作非托管磁盘的页 Blob 的操作价格
               </h4>
               <p>
                针对附加到虚拟机且用作非托管磁盘的标准页 Blob，按每 10,000 个事务收取费用。针对非托管磁盘的各类型操作均被视为事务，包括读取、写入和删除。
               </p>
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv2-unmanaged-disks" width="100%">
                <tbody>
                 <tr>
                  <th align="left">
                   <string>
                   </string>
                  </th>
                  <th align="left">
                   <strong>
                    价格
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   每 10,000 个事务
                  </td>
                  <td align="left">
                   ¥0.0051
                  </td>
                 </tr>
                </tbody>
               </table>
              </div>
              <div class="scroll-table" style="display: block;">
               <h4>
                页 Blob（非磁盘）的操作价格
               </h4>
               <p>
                如果页 blob 事务不是通过 VM 执行的，则按事务对其计费。对于每个事务，单次操作应被视为写入、读取或删除操作计费。若读取或写入事务的大小超过 64 KB，则每超过 64 KB 需支付一次额外的读取或写入输入/输出
                                                        (IO) 费用，每个事务最多收取
                                                        15 个 IO 的费用。如果事务大小超过 1 MB [64 KB x (1 个事务 + 15 个 IO)]，则无论事务大小，不再对其他 IO 计费。
               </p>
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv2-standard-non-disk" width="100%">
                <tbody>
                 <tr>
                  <th align="left">
                   <strong>
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    本地冗余存储 (LRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    异地冗余存储 (GRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    读取访问跨异地冗余存储 (RA-GRS)
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   写入操作
                   <sup>
                    1
                   </sup>
                   （按 10,000 计）
                  </td>
                  <td align="left">
                   ￥0.0153
                  </td>
                  <td align="left">
                   ￥0.3053
                  </td>
                  <td align="left">
                   ￥0.3053
                  </td>
                 </tr>
                 <tr>
                  <td>
                   写入额外 IO 单位（按 10,000 计）
                  </td>
                  <td align="left">
                   ￥0.0237
                  </td>
                  <td align="left">
                   ￥0.0474
                  </td>
                  <td align="left">
                   ￥0.0474
                  </td>
                 </tr>
                 <tr>
                  <td>
                   读取操作
                   <sup>
                    2
                   </sup>
                   （按 10,000 计）
                  </td>
                  <td align="left">
                   ￥0.0153
                  </td>
                  <td align="left">
                   ￥0.0153
                  </td>
                  <td align="left">
                   ￥0.0153
                  </td>
                 </tr>
                 <tr>
                  <td>
                   读取额外 IO 单位（按 10,000 计）
                  </td>
                  <td align="left">
                   ￥0.0017
                  </td>
                  <td align="left">
                   ￥0.0017
                  </td>
                  <td align="left">
                   ￥0.0017
                  </td>
                 </tr>
                 <tr>
                  <td>
                   删除操作
                  </td>
                  <td align="left">
                   免费
                  </td>
                  <td align="left">
                   免费
                  </td>
                  <td align="left">
                   免费
                  </td>
                 </tr>
                </tbody>
               </table>
               <div class="tags-date">
                <div class="ms-date">
                 <sup>
                  1
                 </sup>
                 以下 API
                                                            调用视为写入操作：AbortCopyBlob、AppendBlock、BreakBlobLease、BreakContainerLease、ChangeBlobLease、ChangeContainerLease、CopyBlob、CreateContainer、IncrementalCopyBlob、PutBlob、PutGeoMessage、PutGeoRepairMessageMeasurementEvent、PutGeoVerificationMessageMeasurementEvent、PutPage、SetBlobMetadata、SetBlobProperties、SetBlobServiceProperties、SetContainerACL、SetContainerMetadata、SnapshotBlob
                                                            和 UndeleteBlob。
                </div>
                <br/>
                <div class="ms-date">
                 <sup>
                  2
                 </sup>
                 以下 API
                                                            调用视为读取操作：AcquireBlobLease、AcquireContainerLease、BlobPreflightRequest、GeoBootstrap、GetBlob、GetBlobLeaseInfo、GetBlobMetadata、GetBlobProperties、GetBlobServiceProperties、GetBlobServiceStats、GetBlockList、GetContainerACL、GetContainerMetadata、GetContainerProperties、GetCopyInformation、GetEncryptionKey、GetPageRegions、ReleaseBlobLease、ReleaseContainerLease、RenewBlobLease
                                                            和 RenewContainerLease。
                </div>
               </div>
              </div>
             </div>
             <div class="tab-panel" id="tabContent-gv1">
              <p>
               常规用途 v1 的事务价格最低，存储价格较高，通过它可以访问块 Blob、页 Blob、文件、队列和表。常规用途 v1 帐户不支持访问冷存储和存档存储。
              </p>
              <div class="scroll-table" style="display: block;">
               <h4>
                高级页 Blob 的价格
               </h4>
               <p>
                高级非托管磁盘是基于固态硬盘 (SSD) 的高性能存储器，旨在通过极大吞吐量和极低延迟来支持 I/O 密集型工作负荷。借助高级非托管磁盘，可以设置永久磁盘，并配置其大小和性能特征。
               </p>
               <p>
                高级非托管磁盘的总费用取决于磁盘的大小和数量，并且受到
                <a href="../../data-transfer/index.html" id="pricing_storage_data-transfer-2">
                 数据传输
                </a>
                量的影响。这些磁盘大小提供不同的每秒输入/输出操作数
                                                        (IOP)、吞吐量限制和每 GB 每月价格。可选择最符合应用程序的所需存储大小、IOP 和吞吐量的选项。可以将若干个永久磁盘附加到 VM 并对于每个 VM 配置最多 64 TB 的存储，，每个 VM 的磁盘吞吐量可达到
                                                        80,000（每秒输入/输出操作数）和 1600 MB/秒，读取操作的延迟少于 1 毫秒。高级非托管磁盘受专门针对高级磁盘的 DS 系列、DSv2 系列、 FS 系列 VM 大小的支持。DS 系列虚拟机的定价和计费标准与
                                                        D 系列虚拟机相同。
               </p>
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
                <div class="ms-date">
                 *每月价格估算基于每个月 744 小时的使用量。
                </div>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv1-premium-east3" width="100%">
                <tbody>
                    <tr>
                     <th align="left">
                      <strong>
                       页 BLOB 类型
                      </strong>
                     </th>
                     <th align="left">
                      <strong>
                       P10
                      </strong>
                     </th>
                     <th align="left">
                      <strong>
                       P20
                      </strong>
                     </th>
                     <th align="left">
                      <strong>
                       P30
                      </strong>
                     </th>
                     <th align="left">
                      <strong>
                       P40
                      </strong>
                     </th>
                     <th align="left">
                      <strong>
                       P50
                      </strong>
                     </th>
                     <th align="left">
                      <strong>
                       P60
                       <sup>
                        1
                       </sup>
                      </strong>
                     </th>
                     <th align="left">
                       <strong>
                        P70
                       </strong>
                      </th>
                      <th align="left">
                       <strong>
                        P80
                       </strong>
                      </th>
                    </tr>
                    <tr>
                     <td>
                      磁盘大小
                     </td>
                     <td>
                      128 GB
                     </td>
                     <td>
                      512 GB
                     </td>
                     <td>
                      1 TB
                     </td>
                     <td>
                      2 TB
                     </td>
                     <td>
                      4 TB
                     </td>
                     <td>
                      8 TB
                     </td>
                     <td>
                       128 TB
                     </td>
                     <td>
                       512 TB
                     </td>
                    </tr>
                    <tr>
                     <td>
                      每月价格
                     </td>
                     <td>
                        ￥117.61
                     </td>
                     <td>
                      ￥436.93
                     </td>
                     <td>
                      ￥806.59
                     </td>
                     <td>
                      ￥1,545.77
                     </td>
                     <td>
                      ￥2,957.12
                     </td>
                     <td>
                      ￥5,914.24
                     </td>
                     <td>
                       ￥62,521
                     </td>
                     <td>
                       ￥125,042
                     </td>
                    </tr>
                    <tr>
                     <td>
                      每个页 Blob 的 IOPS
                     </td>
                     <td>
                      500
                     </td>
                     <td>
                      2,300
                     </td>
                     <td>
                      5,000
                     </td>
                     <td>
                      7,500
                     </td>
                     <td>
                      7,500
                     </td>
                     <td>
                      7,500
                     </td>
                     <td>
                       18,000
                     </td>
                     <td>
                       20,000
                     </td>
                    </tr>
                    <tr>
                     <td>
                      每个页 Blob 的吞吐量
                     </td>
                     <td>
                      100 MB / 秒
                     </td>
                     <td>
                      150 MB / 秒
                     </td>
                     <td>
                      200 MB / 秒
                     </td>
                     <td>
                      250 MB / 秒
                     </td>
                     <td>
                      250 MB / 秒
                     </td>
                     <td>
                      250 MB / 秒
                     </td>
                     <td>
                       750 MB / 秒
                     </td>
                     <td>
                       900 MB / 秒
                     </td>
                    </tr>
                   </tbody>
               </table>
               
               <div class="tags-date">
                <div class="ms-date">
                 高级磁盘没有事务处理费用。根据支持目标大小的最低类型对高级页 Blob 收费。
                </div>
                <div class="ms-date">
                 <sup>
                  1
                 </sup>
                 Azure 不支持将 P60 作为虚拟机的磁盘。 页 Blob 支持通过 REST 访问 P60。
                </div>
               </div>
              </div>
              <div class="scroll-table" style="display: block;">
               <h4>
                高级页 Blob 的价格
               </h4>
               <p>
                高级非托管磁盘是基于固态硬盘 (SSD) 的高性能存储器，旨在通过极大吞吐量和极低延迟来支持 I/O 密集型工作负荷。借助高级非托管磁盘，可以设置永久磁盘，并配置其大小和性能特征。
               </p>
               <p>
                高级非托管磁盘的总费用取决于磁盘的大小和数量，并且受到
                <a href="../../data-transfer/index.html" id="pricing_storage_data-transfer-2">
                 数据传输
                </a>
                量的影响。这些磁盘大小提供不同的每秒输入/输出操作数
                                                        (IOP)、吞吐量限制和每 GB 每月价格。可选择最符合应用程序的所需存储大小、IOP 和吞吐量的选项。可以将若干个永久磁盘附加到 VM 并对于每个 VM 配置最多 64 TB 的存储，，每个 VM 的磁盘吞吐量可达到
                                                        80,000（每秒输入/输出操作数）和 1600 MB/秒，读取操作的延迟少于 1 毫秒。高级非托管磁盘受专门针对高级磁盘的 DS 系列、DSv2 系列、 FS 系列 VM 大小的支持。DS 系列虚拟机的定价和计费标准与
                                                        D 系列虚拟机相同。
               </p>
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
                <div class="ms-date">
                 *每月价格估算基于每个月 744 小时的使用量。
                </div>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv1-premium" width="100%">
                <tbody>
                    <tr>
                     <th align="left">
                      <strong>
                       页 BLOB 类型
                      </strong>
                     </th>
                     <th align="left">
                      <strong>
                       P10
                      </strong>
                     </th>
                     <th align="left">
                      <strong>
                       P20
                      </strong>
                     </th>
                     <th align="left">
                      <strong>
                       P30
                      </strong>
                     </th>
                     <th align="left">
                      <strong>
                       P40
                      </strong>
                     </th>
                     <th align="left">
                      <strong>
                       P50
                      </strong>
                     </th>
                     <th align="left">
                      <strong>
                       P60
                       <sup>
                        1
                       </sup>
                      </strong>
                     </th>
                     <th align="left">
                       <strong>
                        P70
                       </strong>
                      </th>
                      <th align="left">
                       <strong>
                        P80
                       </strong>
                      </th>
                    </tr>
                    <tr>
                     <td>
                      磁盘大小
                     </td>
                     <td>
                      128 GB
                     </td>
                     <td>
                      512 GB
                     </td>
                     <td>
                      1 TB
                     </td>
                     <td>
                      2 TB
                     </td>
                     <td>
                      4 TB
                     </td>
                     <td>
                      8 TB
                     </td>
                     <td>
                       128 TB
                     </td>
                     <td>
                       512 TB
                     </td>
                    </tr>
                    <tr>
                     <td>
                      每月价格
                     </td>
                     <td>
                        ￥117.61
                     </td>
                     <td>
                      ￥436.93
                     </td>
                     <td>
                      ￥806.59
                     </td>
                     <td>
                      ￥1,545.77
                     </td>
                     <td>
                      ￥2,957.12
                     </td>
                     <td>
                      ￥5,914.24
                     </td>
                     <td>
                       ￥62,521
                     </td>
                     <td>
                       ￥125,042
                     </td>
                    </tr>
                    <tr>
                     <td>
                      每个页 Blob 的 IOPS
                     </td>
                     <td>
                      500
                     </td>
                     <td>
                      2,300
                     </td>
                     <td>
                      5,000
                     </td>
                     <td>
                      7,500
                     </td>
                     <td>
                      7,500
                     </td>
                     <td>
                      7,500
                     </td>
                     <td>
                       18,000
                     </td>
                     <td>
                       20,000
                     </td>
                    </tr>
                    <tr>
                     <td>
                      每个页 Blob 的吞吐量
                     </td>
                     <td>
                      100 MB / 秒
                     </td>
                     <td>
                      150 MB / 秒
                     </td>
                     <td>
                      200 MB / 秒
                     </td>
                     <td>
                      250 MB / 秒
                     </td>
                     <td>
                      250 MB / 秒
                     </td>
                     <td>
                      250 MB / 秒
                     </td>
                     <td>
                       750 MB / 秒
                     </td>
                     <td>
                       900 MB / 秒
                     </td>
                    </tr>
                   </tbody>
               </table>
               <div class="tags-date">
                <div class="ms-date">
                 高级磁盘没有事务处理费用。根据支持目标大小的最低类型对高级页 Blob 收费。
                </div>
                <div class="ms-date">
                 <sup>
                  1
                 </sup>
                 Azure 不支持将 P60，P70和P80 作为虚拟机的磁盘。 页 Blob 支持通过 REST 访问 P60，P70和P80。
                </div>
               </div>
              </div>
              <div class="scroll-table" style="display: block;">
               <p>
                我们还额外支持三个页 blob 类型，这要求你使用
                <a href="https://docs.microsoft.com/rest/api/storageservices/set-blob-tier">
                 Blob
                                                            层集
                </a>
                进行显式部署。如果你部署了仅指定目标大小的页
                                                        blob 或非托管磁盘，则将向上舍入到上表中列出的受支持的页 blob 类型。
               </p>
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
                <div class="ms-date">
                 *每月价格估算基于每个月 744 小时的使用量。
                </div>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv1-premium-addition" width="100%">
                <tbody>
                 <tr>
                  <th align="left">
                  </th>
                  <th align="left">
                   <strong>
                    P4
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    P6
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    P15
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   磁盘大小
                  </td>
                  <td>
                   32 GB
                  </td>
                  <td>
                   64 GB
                  </td>
                  <td>
                   256 GB
                  </td>
                 </tr>
                 <tr>
                  <td>
                   每月价格
                  </td>
                  <td>
                   ¥ 47.53
                  </td>
                  <td>
                   ¥ 91.9
                  </td>
                  <td>
                   ¥ 254.86
                  </td>
                 </tr>
                 <tr>
                  <td>
                   每个页 Blob 的 IOP
                  </td>
                  <td>
                   120
                  </td>
                  <td>
                   240
                  </td>
                  <td>
                   1,100
                  </td>
                 </tr>
                 <tr>
                  <td>
                   每个 页Blob 的吞吐量
                  </td>
                  <td>
                   25 MB/秒
                  </td>
                  <td>
                   50 MB/秒
                  </td>
                  <td>
                   125 MB/秒
                  </td>
                 </tr>
                </tbody>
               </table>
              </div>
              <p>
               快照价格按每月 ￥2.35/GB 的费率计费。
              </p>
              <div class="scroll-table" style="display: block;">
               <h4>
                标准页 Blob 的价格
               </h4>
               <p>
                标准磁盘使用基于硬盘驱动器 (HDD) 的存储介质。它们最适合于对性能变化不太敏感的开发/测试和其他不频繁的访问工作负荷。
               </p>
               <p>
                标准非托管磁盘存储按存储的数据大小 (GB) 、事务数量以及
                <a href="../../data-transfer/index.html" id="pricing_storage_data-transfer-3">
                 数据传输
                </a>
                量收费。
               </p>
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv1-standard-data-storage-east3" width="100%">
                <tbody>
                 <tr>
                  <!-- <th align="left"><strong>每月存储容量</strong></th> -->
                  <th align="left">
                   <strong>
                    本地冗余存储 (LRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    异地冗余存储 (GRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    读取访问跨异地冗余存储 (RA-GRS)
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <!-- <td>0 至 1 TB</td> -->
                  <td>
                   ¥ 0.595296/GB
                  </td>
                  <td>
                   ¥ 0.793728/GB
                  </td>
                  <td>
                   ¥ 0.99216/GB
                  </td>
                 </tr>
                </tbody>
               </table>
              </div>
              <div class="scroll-table" style="display: block;">
               <h4>
                标准页 Blob 的价格
               </h4>
               <p>
                标准磁盘使用基于硬盘驱动器 (HDD) 的存储介质。它们最适合于对性能变化不太敏感的开发/测试和其他不频繁的访问工作负荷。
               </p>
               <p>
                标准非托管磁盘存储按存储的数据大小 (GB) 、事务数量以及
                <a href="../../data-transfer/index.html" id="pricing_storage_data-transfer-3">
                 数据传输
                </a>
                量收费。
               </p>
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv1-standard-data-storage-north3" width="100%">
                <tbody>
                 <tr>
                  <!-- <th align="left"><strong>每月存储容量</strong></th> -->
                  <th align="left">
                   <strong>
                    本地冗余存储 (LRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    异地冗余存储 (GRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    读取访问跨异地冗余存储 (RA-GRS)
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <!-- <td>0 至 1 TB</td> -->
                  <td>
                   ¥ 0.45792/GB
                  </td>
                  <td>
                   ¥ 0.61056/GB
                  </td>
                  <td>
                   ¥ 0.7632/GB
                  </td>
                 </tr>
                </tbody>
               </table>
              </div>
              <div class="scroll-table" style="display: block;">
               <h4>
                标准页 Blob 的价格
               </h4>
               <p>
                标准磁盘使用基于硬盘驱动器 (HDD) 的存储介质。它们最适合于对性能变化不太敏感的开发/测试和其他不频繁的访问工作负荷。
               </p>
               <p>
                标准非托管磁盘存储按存储的数据大小 (GB) 、事务数量以及
                <a href="../../data-transfer/index.html" id="pricing_storage_data-transfer-3">
                 数据传输
                </a>
                量收费。
               </p>
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv1-standard-data-storage" width="100%">
                <tbody>
                 <tr>
                  <!-- <th align="left"><strong>每月存储容量</strong></th> -->
                  <th align="left">
                   <strong>
                    本地冗余存储 (LRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    异地冗余存储 (GRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    读取访问跨异地冗余存储 (RA-GRS)
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <!-- <td>0 至 1 TB</td> -->
                  <td>
                   ¥ 0.275/GB
                  </td>
                  <td>
                   ¥ 0.55/GB
                  </td>
                  <td>
                   ¥ 0.66/GB
                  </td>
                 </tr>
                 <!-- <tr>
                                                                    <td>1 TB 至 50 TB</td>
                                                                    <td>¥ 0.275/GB</td>
                                                                    <td>¥ 0.55/GB</td>
                                                                    <td>¥ 0.66/GB</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>50 TB 至 500 TB</td>
                                                                    <td>¥ 0.275/GB</td>
                                                                    <td>¥ 0.55/GB</td>
                                                                    <td>¥ 0.66/GB</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>500 TB 至 1000 TB</td>
                                                                    <td>¥ 0.275/GB</td>
                                                                    <td>¥ 0.55/GB</td>
                                                                    <td>¥ 0.66/GB</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>1000 TB 至 5000 TB</td>
                                                                    <td>¥ 0.275/GB</td>
                                                                    <td>¥ 0.55/GB</td>
                                                                    <td>¥ 0.66/GB</td>
                                                                </tr>
                                                                <tr>
                                                                    <td>大于 5000 TB</td>
                                                                    <td align="left"><a id="pricing_storage_blob-disc_lrs_contact" style="color: #00a8d9;" href="https://support.azure.cn/zh-cn/support/contact">联系我们</a></td>
                                                                    <td align="left"><a id="pricing_storage_blob-disc_grs_contact" style="color: #00a8d9;" href="https://support.azure.cn/zh-cn/support/contact">联系我们</a></td>
                                                                    <td align="left"><a id="pricing_storage_blob-disc_ra-grs_contact" style="color: #00a8d9;" href="https://support.azure.cn/zh-cn/support/contact">联系我们</a></td>
                                                                </tr> -->
                </tbody>
               </table>
              </div>
              <!-- <p><strong><sup>1 </sup></strong>1 TB = 1,024 GB</p> -->
              <div class="scroll-table" style="display: block;">
               <h4>
                用作非托管磁盘的页 Blob 的操作价格
               </h4>
               <p>
                针对附加到虚拟机且用作非托管磁盘的标准页 Blob，，按每 10,000 个事务收取费用。针对非托管磁盘的各类型操作均被视为事务，包括读取、写入和删除。
               </p>
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv1-unmanaged-disks-east3" width="100%">
                <tbody>
                 <tr>
                  <th align="left">
                   <string>
                   </string>
                  </th>
                  <th align="left">
                   <strong>
                    价格
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   每 10,000 个事务
                  </td>
                  <td align="left">
                   ￥0.006614
                  </td>
                 </tr>
                </tbody>
               </table>
              </div>
              <div class="scroll-table" style="display: block;">
               <h4>
                用作非托管磁盘的页 Blob 的操作价格
               </h4>
               <p>
                针对附加到虚拟机且用作非托管磁盘的标准页 Blob，，按每 10,000 个事务收取费用。针对非托管磁盘的各类型操作均被视为事务，包括读取、写入和删除。
               </p>
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv1-unmanaged-disks-north3" width="100%">
                <tbody>
                 <tr>
                  <th align="left">
                   <string>
                   </string>
                  </th>
                  <th align="left">
                   <strong>
                    价格
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   每 10,000 个事务
                  </td>
                  <td align="left">
                   ￥0.005088
                  </td>
                 </tr>
                </tbody>
               </table>
              </div>
              <div class="scroll-table" style="display: block;">
               <h4>
                用作非托管磁盘的页 Blob 的操作价格
               </h4>
               <p>
                针对附加到虚拟机且用作非托管磁盘的标准页 Blob，，按每 10,000 个事务收取费用。针对非托管磁盘的各类型操作均被视为事务，包括读取、写入和删除。
               </p>
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv1-unmanaged-disks" width="100%">
                <tbody>
                 <tr>
                  <th align="left">
                   <string>
                   </string>
                  </th>
                  <th align="left">
                   <strong>
                    价格
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   每 10,000 个事务
                  </td>
                  <td align="left">
                   ￥0.0037
                  </td>
                 </tr>
                </tbody>
               </table>
              </div>
              <div class="scroll-table" style="display: block;">
               <h4>
                页 Blob（非磁盘）的操作价格
               </h4>
               <p>
                如果页 blob 事务不是通过虚拟机执行的，则按事务对其计费。对于每个事务，单次操作应被视为写入、读取或删除操作计费。若读取或写入事务的大小超过 64 KB，则每超过 64 KB 需支付一次额外的读取或写入输入/输出
                                                        (IO) 费用，每个事务最多收取
                                                        15 个 IO 的费用。如果事务大小超过 1 MB [64 KB x (1 个事务 + 15 个 IO)]，则无论事务大小，不再对其他 IO 计费。
               </p>
               <!-- <p>对于标准非托管磁盘和页 Blob，我们按每 10,000 个事务收取 ￥0.0044 的费用。针对存储的任何类型的操作均视为事务，包括读取、写入和删除。</p> -->
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv1-standard-non-disk-east3" width="100%">
                <tbody>
                 <tr>
                  <th align="left">
                   <strong>
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    本地冗余存储 (LRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    异地冗余存储 (GRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    读取访问跨异地冗余存储 (RA-GRS)
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   写入操作
                   <sup>
                    1
                   </sup>
                   （按 10,000 计）
                  </td>
                  <td align="left">
                   ￥0.019843
                  </td>
                  <td align="left">
                   ￥0.396864
                  </td>
                  <td align="left">
                   ￥0.396864
                  </td>
                 </tr>
                 <tr>
                  <td>
                   写入额外 IO 单位（按 10,000 计）
                  </td>
                  <td align="left">
                   ￥0.030823
                  </td>
                  <td align="left">
                   ￥0.061646
                  </td>
                  <td align="left">
                   ￥0.061646
                  </td>
                 </tr>
                 <tr>
                  <td>
                   读取操作
                   <sup>
                    2
                   </sup>
                   （按 10,000 计）
                  </td>
                  <td align="left">
                   ￥0.019843
                  </td>
                  <td align="left">
                   ￥0.019843
                  </td>
                  <td align="left">
                   ￥0.019843
                  </td>
                 </tr>
                 <tr>
                  <td>
                   读取额外 IO 单位（按 10,000 计）
                  </td>
                  <td align="left">
                   ￥0.002249
                  </td>
                  <td align="left">
                   ￥0.002249
                  </td>
                  <td align="left">
                   ￥0.002249
                  </td>
                 </tr>
                 <tr>
                  <td>
                   删除操作
                  </td>
                  <td align="left">
                   免费
                  </td>
                  <td align="left">
                   免费
                  </td>
                  <td align="left">
                   免费
                  </td>
                 </tr>
                </tbody>
               </table>
               <div class="tags-date">
                <div class="ms-date">
                 <sup>
                  1
                 </sup>
                 以下 API
                                                            调用视为写入操作：AbortCopyBlob、AppendBlock、BreakBlobLease、BreakContainerLease、ChangeBlobLease、ChangeContainerLease、CopyBlob、CreateContainer、IncrementalCopyBlob、PutBlob、PutGeoMessage、PutGeoRepairMessageMeasurementEvent、PutGeoVerificationMessageMeasurementEvent、PutPage、SetBlobMetadata、SetBlobProperties、SetBlobServiceProperties、SetContainerACL、SetContainerMetadata、SnapshotBlob
                                                            和 UndeleteBlob。
                </div>
                <br/>
                <div class="ms-date">
                 <sup>
                  2
                 </sup>
                 以下 API
                                                            调用视为读取操作：AcquireBlobLease、AcquireContainerLease、BlobPreflightRequest、GeoBootstrap、GetBlob、GetBlobLeaseInfo、GetBlobMetadata、GetBlobProperties、GetBlobServiceProperties、GetBlobServiceStats、GetBlockList、GetContainerACL、GetContainerMetadata、GetContainerProperties、GetCopyInformation、GetEncryptionKey、GetPageRegions、ReleaseBlobLease、ReleaseContainerLease、RenewBlobLease
                                                            和 RenewContainerLease。
                </div>
               </div>
              </div>
              <div class="scroll-table" style="display: block;">
               <h4>
                页 Blob（非磁盘）的操作价格
               </h4>
               <p>
                如果页 blob 事务不是通过虚拟机执行的，则按事务对其计费。对于每个事务，单次操作应被视为写入、读取或删除操作计费。若读取或写入事务的大小超过 64 KB，则每超过 64 KB 需支付一次额外的读取或写入输入/输出
                                                        (IO) 费用，每个事务最多收取
                                                        15 个 IO 的费用。如果事务大小超过 1 MB [64 KB x (1 个事务 + 15 个 IO)]，则无论事务大小，不再对其他 IO 计费。
               </p>
               <!-- <p>对于标准非托管磁盘和页 Blob，我们按每 10,000 个事务收取 ￥0.0044 的费用。针对存储的任何类型的操作均视为事务，包括读取、写入和删除。</p> -->
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv1-standard-non-disk-north3" width="100%">
                <tbody>
                 <tr>
                  <th align="left">
                   <strong>
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    本地冗余存储 (LRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    异地冗余存储 (GRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    读取访问跨异地冗余存储 (RA-GRS)
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   写入操作
                   <sup>
                    1
                   </sup>
                   （按 10,000 计）
                  </td>
                  <td align="left">
                   ￥0.015264
                  </td>
                  <td align="left">
                   ￥0.30528
                  </td>
                  <td align="left">
                   ￥0.30528
                  </td>
                 </tr>
                 <tr>
                  <td>
                   写入额外 IO 单位（按 10,000 计）
                  </td>
                  <td align="left">
                   ￥0.02371
                  </td>
                  <td align="left">
                   ￥0.04742
                  </td>
                  <td align="left">
                   ￥0.04742
                  </td>
                 </tr>
                 <tr>
                  <td>
                   读取操作
                   <sup>
                    2
                   </sup>
                   （按 10,000 计）
                  </td>
                  <td align="left">
                   ￥0.015264
                  </td>
                  <td align="left">
                   ￥0.015264
                  </td>
                  <td align="left">
                   ￥0.015264
                  </td>
                 </tr>
                 <tr>
                  <td>
                   读取额外 IO 单位（按 10,000 计）
                  </td>
                  <td align="left">
                   ￥0.00173
                  </td>
                  <td align="left">
                   ￥0.00173
                  </td>
                  <td align="left">
                   ￥0.00173
                  </td>
                 </tr>
                 <tr>
                  <td>
                   删除操作
                  </td>
                  <td align="left">
                   免费
                  </td>
                  <td align="left">
                   免费
                  </td>
                  <td align="left">
                   免费
                  </td>
                 </tr>
                </tbody>
               </table>
               <div class="tags-date">
                <div class="ms-date">
                 <sup>
                  1
                 </sup>
                 以下 API
                                                            调用视为写入操作：AbortCopyBlob、AppendBlock、BreakBlobLease、BreakContainerLease、ChangeBlobLease、ChangeContainerLease、CopyBlob、CreateContainer、IncrementalCopyBlob、PutBlob、PutGeoMessage、PutGeoRepairMessageMeasurementEvent、PutGeoVerificationMessageMeasurementEvent、PutPage、SetBlobMetadata、SetBlobProperties、SetBlobServiceProperties、SetContainerACL、SetContainerMetadata、SnapshotBlob
                                                            和 UndeleteBlob。
                </div>
                <br/>
                <div class="ms-date">
                 <sup>
                  2
                 </sup>
                 以下 API
                                                            调用视为读取操作：AcquireBlobLease、AcquireContainerLease、BlobPreflightRequest、GeoBootstrap、GetBlob、GetBlobLeaseInfo、GetBlobMetadata、GetBlobProperties、GetBlobServiceProperties、GetBlobServiceStats、GetBlockList、GetContainerACL、GetContainerMetadata、GetContainerProperties、GetCopyInformation、GetEncryptionKey、GetPageRegions、ReleaseBlobLease、ReleaseContainerLease、RenewBlobLease
                                                            和 RenewContainerLease。
                </div>
               </div>
              </div>
              <div class="scroll-table" style="display: block;">
               <h4>
                页 Blob（非磁盘）的操作价格
               </h4>
               <p>
                如果页 blob 事务不是通过虚拟机执行的，则按事务对其计费。对于每个事务，单次操作应被视为写入、读取或删除操作计费。若读取或写入事务的大小超过 64 KB，则每超过 64 KB 需支付一次额外的读取或写入输入/输出
                                                        (IO) 费用，每个事务最多收取
                                                        15 个 IO 的费用。如果事务大小超过 1 MB [64 KB x (1 个事务 + 15 个 IO)]，则无论事务大小，不再对其他 IO 计费。
               </p>
               <!-- <p>对于标准非托管磁盘和页 Blob，我们按每 10,000 个事务收取 ￥0.0044 的费用。针对存储的任何类型的操作均视为事务，包括读取、写入和删除。</p> -->
               <div class="tags-date">
                <div class="ms-date">
                 *以下价格均为含税价格。
                </div>
                <br/>
               </div>
               <table cellpadding="0" cellspacing="0" id="page-blobs-gv1-standard-non-disk" width="100%">
                <tbody>
                 <tr>
                  <th align="left">
                   <strong>
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    本地冗余存储 (LRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    异地冗余存储 (GRS)
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    读取访问跨异地冗余存储 (RA-GRS)
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   写入操作
                   <sup>
                    1
                   </sup>
                   （按 10,000 计）
                  </td>
                  <td align="left">
                   ￥0.0044
                  </td>
                  <td align="left">
                   ￥0.0044
                  </td>
                  <td align="left">
                   ￥0.0044
                  </td>
                 </tr>
                 <tr>
                  <td>
                   写入额外 IO 单位（按 10,000 计）
                  </td>
                  <td align="left">
                   免费
                  </td>
                  <td align="left">
                   免费
                  </td>
                  <td align="left">
                   免费
                  </td>
                 </tr>
                 <tr>
                  <td>
                   读取操作
                   <sup>
                    2
                   </sup>
                   （按 10,000 计）
                  </td>
                  <td align="left">
                   ￥0.0044
                  </td>
                  <td align="left">
                   ￥0.0044
                  </td>
                  <td align="left">
                   ￥0.0044
                  </td>
                 </tr>
                 <tr>
                  <td>
                   读取额外 IO 单位（按 10,000 计）
                  </td>
                  <td align="left">
                   免费
                  </td>
                  <td align="left">
                   免费
                  </td>
                  <td align="left">
                   免费
                  </td>
                 </tr>
                 <tr>
                  <td>
                   删除操作
                  </td>
                  <td align="left">
                   ￥0.0044
                  </td>
                  <td align="left">
                   ￥0.0044
                  </td>
                  <td align="left">
                   ￥0.0044
                  </td>
                 </tr>
                </tbody>
               </table>
               <div class="tags-date">
                <div class="ms-date">
                 <sup>
                  1
                 </sup>
                 以下 API
                                                            调用视为写入操作：AbortCopyBlob、AppendBlock、BreakBlobLease、BreakContainerLease、ChangeBlobLease、ChangeContainerLease、CopyBlob、CreateContainer、IncrementalCopyBlob、PutBlob、PutGeoMessage、PutGeoRepairMessageMeasurementEvent、PutGeoVerificationMessageMeasurementEvent、PutPage、SetBlobMetadata、SetBlobProperties、SetBlobServiceProperties、SetContainerACL、SetContainerMetadata、SnapshotBlob
                                                            和 UndeleteBlob。
                </div>
                <br/>
                <div class="ms-date">
                 <sup>
                  2
                 </sup>
                 以下 API
                                                            调用视为读取操作：AcquireBlobLease、AcquireContainerLease、BlobPreflightRequest、GeoBootstrap、GetBlob、GetBlobLeaseInfo、GetBlobMetadata、GetBlobProperties、GetBlobServiceProperties、GetBlobServiceStats、GetBlockList、GetContainerACL、GetContainerMetadata、GetContainerProperties、GetCopyInformation、GetEncryptionKey、GetPageRegions、ReleaseBlobLease、ReleaseContainerLease、RenewBlobLease
                                                            和 RenewContainerLease。
                </div>
               </div>
              </div>
             </div>
            </div>
           </div>
          </div>
         </div>
        </div>
       </div>
       <!-- <div class="pricing-page-section">
                        <h2>上市地区</h2>
                        <p>存储服务在以下区域中提供：</p>
                        <table cellpadding="0" cellspacing="0" class="table-col6">
                            <tr>
                                <th align="left"><strong>地域</strong></th>
                                <th align="left"><strong>区域</strong></th>
                            </tr>
                            <tr>
                                <td>中国大陆</td>
                                <td>中国东部数据中心 , 中国北部数据中心</td>
                            </tr>
                        </table>
                    </div> -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="Storage_page_question14">
             为什么我已经删除了磁盘上的数据，可是计费的时候一直按照最大时的磁盘容量计费呢？
            </a>
            <section>
             <p>
              虚拟磁盘在 Azure 上是作为页 Blob 来存储的。页 Blob 是一种稀疏的存储方式，只有实际写入数据的容量会被计费。关于如何释放虚拟磁盘未使用空间来减少计费容量，请参考
              <a href="https://support.azure.cn/docs/azure-operations-guide/virtual-machines/aog-virtual-machines-billing-delete-unused-vhd-to-reduce-cost.html" style="margin: 0;">
               这里
              </a>
              。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="storage-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证至少在 99.99%（对于冷访问层为 99.9%）的时间成功地处理从读取访问异地冗余存储 (RA-GRS) 帐户读取数据的请求，但前提是在次要区域重试从主要区域读取数据的失败尝试
        </p>
        <p>
         我们保证至少在 99.9%（对于冷访问层为 99%）的时间成功地处理从本地冗余存储 (LRS) 和地理冗余存储 (GRS) 帐户读取数据的请求。
        </p>
        <p>
         我们保证至少在 99.9%（对于冷访问层为 99%）的时间成功地处理将数据写入本地冗余存储 (LRS) 和地理冗余存储 (GRS) 帐户，以及读取访问异地冗余存储 (RA-GRS) 帐户的请求。
        </p>
        <p>
         若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../../support/sla/storage/index.html" id="pricing_storage_sla">
          服务级别协议
         </a>
         页。
        </p>
        <p>
         托管磁盘本身不提供有财务方面支持的服务级别协议。托管磁盘的可用性取决于所使用的基础存储和其所依附的虚拟机的服务级别协议。若要了解更多有关托管磁盘的服务级别协议的详细信息，请访问
         <a href="../../../../support/sla/managed-disks/index.html">
          托管磁盘服务级别协议
         </a>
         页。
        </p>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="wABuzhNUcUfdh8re2ysWMEDKfDYOwqIRm_JlL9SQzKjGS7eVU1OrAvm2O-pa4fHWKJ3q5VVtEpksypEgkLttZ8J-aDrnGEa9geJQs8JBpWc1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src=" /Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <script src="../../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
  </script>
  <!-- end JSLL -->
  <script src="../../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
