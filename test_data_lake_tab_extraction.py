#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Data Lake Storage tab 内容提取功能
"""

import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from src.extractors.enhanced_cms_extractor import EnhancedCMSExtractor


def test_data_lake_tab_extraction():
    """测试 Data Lake Storage tab 内容提取"""
    
    print("🧪 开始测试 Data Lake Storage tab 内容提取")
    
    # 初始化提取器
    extractor = EnhancedCMSExtractor(
        output_dir="output",
        config_file="data/configs/soft-category.json"
    )
    
    # 测试文件路径
    html_file = "data/prod-html/storage_data-lake_index.html"
    
    if not Path(html_file).exists():
        print(f"❌ 测试文件不存在: {html_file}")
        return
    
    print(f"📁 测试文件: {html_file}")
    
    try:
        # 提取内容
        result = extractor.extract_cms_content(html_file)
        
        print("\n📊 提取结果概览:")
        print(f"  标题: {result.get('Title', 'N/A')}")
        print(f"  产品: {result.get('MSServiceName', 'N/A')}")
        print(f"  有区域: {result.get('HasRegion', False)}")
        
        # 检查区域内容
        if result.get('HasRegion'):
            print("\n🌏 区域内容:")
            for key, value in result.items():
                if key.endswith('Content') and key != 'NoRegionContent':
                    print(f"  {key}:")
                    if isinstance(value, list):
                        print(f"    类型: 数组 (包含 {len(value)} 个 tab)")
                        for i, tab in enumerate(value):
                            if isinstance(tab, dict) and 'tabName' in tab:
                                content_length = len(tab.get('content', ''))
                                print(f"      Tab {i+1}: {tab['tabName']} ({content_length} 字符)")
                            else:
                                print(f"      Tab {i+1}: 格式异常")
                    else:
                        print(f"    类型: 字符串 ({len(str(value))} 字符)")
        else:
            print("\n📄 无区域内容:")
            no_region_content = result.get('NoRegionContent', '')
            if isinstance(no_region_content, list):
                print(f"  类型: 数组 (包含 {len(no_region_content)} 个 tab)")
                for i, tab in enumerate(no_region_content):
                    if isinstance(tab, dict) and 'tabName' in tab:
                        content_length = len(tab.get('content', ''))
                        print(f"    Tab {i+1}: {tab['tabName']} ({content_length} 字符)")
            else:
                print(f"  类型: 字符串 ({len(str(no_region_content))} 字符)")
        
        # 保存结果到文件
        output_file = "output/data_lake_tab_test_result.json"
        Path("output").mkdir(exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 结果已保存到: {output_file}")
        
        # 显示一个区域的详细内容示例
        print("\n🔍 详细内容示例:")
        for key, value in result.items():
            if key.endswith('Content') and isinstance(value, list) and len(value) > 0:
                print(f"\n{key} 第一个 tab 内容预览:")
                first_tab = value[0]
                if isinstance(first_tab, dict):
                    print(f"  Tab 名称: {first_tab.get('tabName', 'N/A')}")
                    content = first_tab.get('content', '')
                    preview = content[:500] + "..." if len(content) > 500 else content
                    print(f"  内容预览: {preview}")
                break
        
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_data_lake_tab_extraction()
