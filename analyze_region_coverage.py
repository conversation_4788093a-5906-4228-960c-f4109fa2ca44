#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析区域覆盖情况
"""

import json
import sys
from pathlib import Path
from bs4 import BeautifulSoup
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))


def analyze_region_coverage():
    """分析区域覆盖情况"""
    
    print("🔍 分析 Data Lake Storage 区域覆盖情况")
    
    # 1. 读取配置文件中的区域信息
    config_file = "data/configs/soft-category.json"
    with open(config_file, 'r', encoding='utf-8') as f:
        config_data = json.load(f)
    
    # 提取 Data Lake Storage 的区域配置
    data_lake_configs = []
    for item in config_data:
        if item.get("os") == "Azure_Data_Lake_Storage_Gen":
            data_lake_configs.append(item)
    
    print(f"\n📋 配置文件中的区域: {len(data_lake_configs)} 个")
    for config in data_lake_configs:
        region = config["region"]
        table_count = len(config["tableIDs"])
        print(f"  - {region}: {table_count} 个表格")
    
    # 2. 读取HTML文件中的实际表格
    html_file = "data/prod-html/storage_data-lake_index.html"
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    tables = soup.find_all('table', id=True)
    
    print(f"\n📊 HTML文件中的表格: {len(tables)} 个")
    
    # 按区域分组表格
    region_tables = defaultdict(list)
    for table in tables:
        table_id = table.get('id')
        if 'east3' in table_id:
            region_tables['east-china3'].append(table_id)
        elif 'north3' in table_id:
            region_tables['north-china3'].append(table_id)
        elif 'east' in table_id and 'east3' not in table_id:
            region_tables['other-east'].append(table_id)
        elif 'north' in table_id and 'north3' not in table_id:
            region_tables['other-north'].append(table_id)
        else:
            region_tables['unknown'].append(table_id)
    
    print("\n🗺️ HTML中按区域分组的表格:")
    for region, table_list in region_tables.items():
        print(f"  - {region}: {len(table_list)} 个表格")
        for table_id in table_list[:3]:  # 只显示前3个
            print(f"    * {table_id}")
        if len(table_list) > 3:
            print(f"    * ... 还有 {len(table_list) - 3} 个")
    
    # 3. 对比分析
    print(f"\n🔍 对比分析:")
    config_regions = set(config["region"] for config in data_lake_configs)
    html_regions = set(region for region in region_tables.keys() if region not in ['unknown', 'other-east', 'other-north'])
    
    print(f"  配置文件中的区域: {sorted(config_regions)}")
    print(f"  HTML文件中的区域: {sorted(html_regions)}")
    
    missing_in_html = config_regions - html_regions
    extra_in_html = html_regions - config_regions
    
    if missing_in_html:
        print(f"  ⚠️ HTML中缺失的区域: {sorted(missing_in_html)}")
    
    if extra_in_html:
        print(f"  ⚠️ HTML中额外的区域: {sorted(extra_in_html)}")
    
    # 4. 建议
    print(f"\n💡 建议:")
    if len(html_regions) < len(config_regions):
        print("  - 当前HTML文件可能只包含部分区域的内容")
        print("  - 可能需要检查是否有其他HTML文件包含其他区域")
        print("  - 或者考虑基于配置文件生成所有区域的内容结构")
    else:
        print("  - HTML文件包含了所有配置的区域")
    
    print("\n✅ 分析完成")


if __name__ == "__main__":
    analyze_region_coverage()
