#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试区域检测逻辑
"""

import sys
from pathlib import Path
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from src.extractors.enhanced_cms_extractor import EnhancedCMSExtractor


def debug_region_detection():
    """调试区域检测"""
    
    print("🔍 调试区域检测逻辑")
    
    # 读取HTML文件
    html_file = "data/prod-html/storage_data-lake_index.html"
    
    if not Path(html_file).exists():
        print(f"❌ 文件不存在: {html_file}")
        return
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 初始化提取器
    extractor = EnhancedCMSExtractor(
        output_dir="output",
        config_file="data/configs/soft-category.json"
    )
    
    print("\n🔍 检查区域选择器...")
    
    # 1. 检查传统的区域选择器
    region_indicators = [
        soup.find('div', class_='region-container'),
        soup.find('select', class_='region-selector'),
        soup.find('div', class_='software-kind'),
        soup.find('div', attrs={'data-region': True})
    ]
    
    for i, indicator in enumerate(region_indicators):
        if indicator:
            print(f"  ✓ 发现区域选择器 {i+1}: {indicator.name} - {indicator.get('class', [])}")
        else:
            print(f"  ✗ 区域选择器 {i+1}: 未找到")
    
    # 2. 检查区域相关的属性
    region_elements = soup.find_all(attrs={'data-region': True})
    print(f"\n📍 data-region 属性元素: {len(region_elements)} 个")
    for elem in region_elements[:5]:  # 只显示前5个
        print(f"  - {elem.name}: {elem.get('data-region')}")
    
    # 3. 检查表格中的区域信息
    tables_with_regions = soup.find_all('table', id=lambda x: x and any(region in x for region in ['east3', 'north3', 'east-china', 'north-china']))
    print(f"\n📊 包含区域信息的表格: {len(tables_with_regions)} 个")
    for table in tables_with_regions[:5]:  # 只显示前5个
        print(f"  - {table.get('id')}")
    
    # 4. 测试 _check_has_region 方法
    print(f"\n🧪 测试 _check_has_region 方法:")
    has_region = extractor._check_has_region(soup)
    print(f"  结果: {has_region}")
    
    # 5. 测试 _detect_available_regions 方法
    print(f"\n🧪 测试 _detect_available_regions 方法:")
    available_regions = extractor._detect_available_regions(soup)
    print(f"  检测到的区域: {available_regions}")
    
    # 6. 测试 tab 检测
    print(f"\n📂 测试 tab 检测:")
    tab_structure = extractor._detect_tab_structure(soup)
    print(f"  检测到的 tab: {[tab['tabName'] for tab in tab_structure]}")
    
    print("\n✅ 调试完成")


if __name__ == "__main__":
    debug_region_detection()
