关于页面中有tab控制的，准确获取页面内tab内容的方法：



**1. HTML结构**

页面结构：

```
 ├── Tab导航器（ul.os-tab-nav）
 │  ├── Tab1: 平面命名空间 (data-href="#tabContent1-1")
 │  └── Tab2: 分层命名空间 (data-href="#tabContent1-2")
 └── Tab内容区
  ├── div#tabContent1-1 (平面命名空间内容)
  │  └── 多个表格 (table#Azure-Data-Lake-Storage-Gen1-1-east3等)
  └── div#tabContent1-2 (分层命名空间内容)
    └── 多个表格 (table#Azure-Data-Lake-Storage-Gen2-1-east3等)
```



**2. soft-category.json结构**

```
{
 "os": "产品名称",
 "region": "区域名",
 "tableIDs": ["#需要排除的表格ID1", "#需要排除的表格ID2"]
 }
```



**3.** **如果导出目标为JSON的结构**

``` json
{ 
     "Title": "API 管理定价 - Azure云计算",
     "MetaDescription": "查看 Azure Data Lake Storage Gen2（用于数据存储的企业级云存储服务）的定价详情。无前期成本。访问价格相关报价。",
     "MetaKeywords": "Azure, 微软云, Azure 存储 - Data Lake Storage, 价格详情, 定价, 计费",
     "MSServiceName": "data-lake",
     "Slug": "storage/data-lake",
     "DescriptionContent": "",
     "Language": "zh-CN",
     "NavigationTitle": "价格详情-存储-DataLake",
     "BannerContent": "",
     "QaContent": "",
     "HasRegion": true,
     "NoRegionContent": "",
     "EastChina2Content": [
      {
        "tabName": "平面命名空间",
        "content": "该tab下的所有内容（已过滤）"
      },
      {
        "tabName": "分层命名空间", 
        "content": "该tab下的所有内容（已过滤）"
      }
     ]
 }
```



**二、核心处理逻辑**

**步骤1：初始化数据结构**

在页面内查找该产品适用的区域列表：region筛选列表中的地区

加载`soft-category.json`配置文件

**步骤2：识别Tab结构**

1. 查找tab导航元素（ul.os-tab-nav）
2. 遍历所有`<a>`标签，提取：
- Tab名称（标签文本）
- 目标内容ID（data-href属性值，需去除#前缀）

3. 建立Tab映射关系：{tabName: "平面命名空间", contentId: "tabContent1-1"}

**步骤3：处理每个区域的内容**

对于每个区域（如east-china3）：

1. 获取排除列表

2. - 从soft-category.json中查找匹配的记录：

   - - os = "Azure_Data_Lake_Storage_Gen"
     - region = 当前区域

   - 提取tableIDs数组作为排除列表

3. 处理每个Tab内容

4. 在下面的div class="tab-content"层内，开始逐层提取内容，如果：
   - 找到tab选择器对应的内容div（通过contentId）
   - 对该div内的内容进行过滤处理
   - 将处理后的内容保存
   
5. 表格过滤逻辑

6. - 找到tab内容div中的所有<table>元素

   - 对每个表格：

   - - 获取表格的id属性
     - 检查"#"   + table.id是否在排除列表中
     - 如果在排除列表中 → 移除该表格及其容器（通常是父级的div.scroll-table）
     - 如果不在排除列表中 → 保留该表格

输出中保留tableId

1. 内容提取

2. - 提取表格前的标题
   - 提取说明文字（<p>）
   - 保留注释说明（通常在div.ms-date中）

**步骤4：组装区域内容**

将所有Tab的内容组装成列表格式：

```
xxxregionContent = [
 {
   "tabName": "平面命名空间",
   "content": "处理后的内容字符串"
 },
 {
   "tabName": "分层命名空间",
   "content": "处理后的内容字符串"
 }
 ]
```



**步骤5：处理无区域限制内容**

1. 提取不属于任何tab的通用内容
2. 提取页面级别的说明、注意事项等
3. 这些内容通常位于tab结构之外

**三、特殊处理注意事项**

**1.** **边界情况处理**

- Tab内容为空的情况
- 表格没有ID的情况
- soft-category.json中没有对应区域配置的情况